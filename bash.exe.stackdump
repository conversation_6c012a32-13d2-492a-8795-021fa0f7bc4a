Stack trace:
Frame         Function      Args
0007FFFF9DD0  00021006118E (00021028DEE8, 000210272B3E, 0007FFFF9DD0, 0007FFFF8CD0) msys-2.0.dll+0x2118E
0007FFFF9DD0  0002100469BA (000000000000, 000000000000, 000000000000, 0007FFFFA0A8) msys-2.0.dll+0x69BA
0007FFFF9DD0  0002100469F2 (00021028DF99, 0007FFFF9C88, 0007FFFF9DD0, 000000000000) msys-2.0.dll+0x69F2
0007FFFF9DD0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF9DD0  00021006A545 (0007FFFF9DE0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0007FFFFA0B0  00021006B9A5 (0007FFFF9DE0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFA20300000 ntdll.dll
7FFA1F000000 KERNEL32.DLL
7FFA1D660000 KERNELBASE.dll
7FFA1FBF0000 USER32.dll
7FFA1D4E0000 win32u.dll
7FFA1FB30000 GDI32.dll
000210040000 msys-2.0.dll
7FFA1DF60000 gdi32full.dll
7FFA1D510000 msvcp_win.dll
7FFA1DC90000 ucrtbase.dll
7FFA1EAB0000 advapi32.dll
7FFA1F330000 msvcrt.dll
7FFA1E9D0000 sechost.dll
7FFA1FE80000 RPCRT4.dll
7FFA1CA80000 CRYPTBASE.DLL
7FFA1D5C0000 bcryptPrimitives.dll
7FFA1E740000 IMM32.DLL
